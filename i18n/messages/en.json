{"metadata": {"title": "AI Prompt Generator - Create Perfect Prompts | Prompt Ark", "description": "Free AI prompt generator for ChatGPT, Claude, Gemini & more. Create optimized prompts with advanced prompt engineering tools. Boost your AI results instantly.", "keywords": "ai prompt generator, prompt generator, prompt engineering, ai prompts, chatgpt prompts, claude prompts, prompt creator, prompt optimizer"}, "user": {"sign_in": "Sign In", "sign_out": "Sign Out", "credits": "Credits", "api_keys": "API Keys", "my_orders": "My Orders", "user_center": "User Center", "admin_system": "Admin System"}, "sign_modal": {"sign_in_title": "Sign In", "sign_in_description": "Sign in to your account", "sign_up_title": "Sign Up", "sign_up_description": "Create an account", "email_title": "Email", "email_placeholder": "Input your email here", "password_title": "Password", "password_placeholder": "Input your password here", "forgot_password": "Forgot password?", "or": "Or", "continue": "Continue", "no_account": "Don't have an account?", "email_sign_in": "Sign in with <PERSON><PERSON>", "google_sign_in": "Sign in with Google", "github_sign_in": "Sign in with GitHub", "close_title": "Close", "cancel_title": "Cancel"}, "my_orders": {"title": "My Orders", "description": "orders paid with ShipAny.", "no_orders": "No orders found", "tip": "", "activate_order": "Activate Order", "actived": "Activated", "join_discord": "Join <PERSON>", "read_docs": "Read Docs", "table": {"order_no": "Order No", "email": "Email", "product_name": "Product Name", "amount": "Amount", "paid_at": "<PERSON><PERSON>", "github_username": "GitHub Username", "status": "Status"}}, "my_credits": {"title": "My Credits", "left_tip": "left credits: {left_credits}", "no_credits": "No credits records", "recharge": "Recharge", "table": {"trans_no": "Trans No", "trans_type": "Trans Type", "credits": "Credits", "updated_at": "Updated At", "status": "Status"}}, "my_favorites": {"title": "My Favorites", "empty_message": "No Prompts favorited yet", "search_placeholder": "Search Prompts...", "add_new": "Add Favorite", "total_count": "{count} favorites in total", "categories": {"title": "Categories", "all": "All", "add_new": "Add Category", "edit": "Edit Category", "edit_category": "Edit Category", "delete": "Delete Category", "uncategorized": "Uncategorized", "edit_dialog": {"title": "Edit Category", "description": "Modify category name", "name_label": "Category Name", "name_placeholder": "Enter category name", "name_required": "Category name is required", "name_too_long": "Category name must not exceed 50 characters", "save_button": "Save", "saving": "Saving...", "cancel_button": "Cancel", "update_success": "Category updated successfully", "update_failed": "Failed to update category"}, "delete_success": "Category deleted successfully", "delete_failed": "Failed to delete category", "delete_failed_has_usage": "Cannot delete category: There are still favorites using this category"}, "card": {"copy": "Copy", "edit": "Edit", "delete": "Delete", "view": "View Details", "copied": "Copied to clipboard", "delete_confirm": "Are you sure you want to delete this favorite?", "delete_success": "Deleted successfully", "delete_failed": "Failed to delete", "copy_success": "Copied to clipboard", "edit_success": "Favorite updated successfully", "edit_failed": "Failed to update favorite"}, "delete_dialog": {"title": "Confirm Delete", "description": "Are you sure you want to delete the favorite ", "warning": " This action cannot be undone.", "confirm_button": "Confirm Delete", "cancel_button": "Cancel", "deleting": "Deleting..."}, "delete_category_dialog": {"title": "Confirm Delete Category", "description": "Are you sure you want to delete the category ", "warning": "", "confirm_button": "Confirm Delete", "cancel_button": "Cancel", "deleting": "Deleting..."}, "toolbar": {"search": "Search", "filter": "Filter", "sort": "Sort", "grid_view": "Grid View", "list_view": "List View"}, "pagination": {"previous": "Previous", "next": "Next", "first": "First", "last": "Last", "page": "Page {page}", "total": "{total} items total", "range": "{start}-{end} of {total}", "items": "items", "loading": "Loading...", "error": "Failed to load", "retry": "Retry", "no_data": "No data", "invalid_page": "Invalid page number, redirected to first page", "more_pages": "More pages"}}, "api_keys": {"title": "API Keys", "tip": "Please keep your apikey safe to avoid leaks", "no_api_keys": "No API Keys", "create_api_key": "Create API Key", "table": {"name": "Name", "key": "Key", "created_at": "Created At"}, "form": {"name": "Name", "name_placeholder": "API Key Name", "submit": "Submit"}}, "blog": {"title": "Blog", "description": "News, resources, and updates about Prompt Ark", "read_more_text": "Read More"}, "my_invites": {"title": "My Invites", "description": "View your invite records", "no_invites": "No invite records found", "my_invite_link": "My Invite Link", "edit_invite_link": "Edit Invite <PERSON>", "copy_invite_link": "Copy Invite Link", "invite_code": "Invite Code", "invite_tip": "Invite 1 friend to buy ShipAny, reward $50.", "invite_balance": "In<PERSON>te <PERSON>", "total_invite_count": "Total Invite Count", "total_paid_count": "Total Paid Count", "total_award_amount": "Total Award Amount", "update_invite_code": "Set Invite Code", "update_invite_code_tip": "Input your custom invite code", "update_invite_button": "Save", "no_orders": "You can't invite others before you bought ShipAny", "no_affiliates": "You're not allowed to invite others, please contact us to apply for permission.", "table": {"invite_time": "Invite Time", "invite_user": "Invite User", "status": "Status", "reward_percent": "<PERSON><PERSON> Percent", "reward_amount": "<PERSON><PERSON> Amount", "pending": "Pending", "completed": "Completed"}}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "We'd love to hear what went well or how we can improve the product experience.", "submit": "Submit", "loading": "Submitting...", "contact_tip": "Other ways to contact us", "rating_tip": "How do you feel about <PERSON><PERSON><PERSON>?", "placeholder": "Leave your words here..."}, "prompt_generator": {"title": "Prompt Generator", "subtitle": "Enter your initial prompt, and click Generate button to enhance it", "input_label": "Input Prompt", "input_placeholder": "Enter the prompt you want to optimize or describe your requirements...", "mode_basic": "Basic Mode", "mode_advanced": "Advanced Mode", "basic_mode_simple_hint": "Quick basic prompt optimization • Free to use", "advanced_mode_simple_hint": "Faster & Professional prompt optimization • Costs 1 credit", "login_required": "🔒 Login required", "login_success_continue": "Login successful! You can continue generating prompts", "model_label": "Select Model", "fast_model": "Fast Model", "fast_model_desc": "Quick generation, suitable for simple prompt optimization", "advanced_model": "Advanced Model", "advanced_model_desc": "Deep optimization with detailed suggestions and improvements", "generate_button": "Generate", "generating": "Generating...", "output_label": "Generated Result", "copy_button": "Copy", "clear_button": "Clear", "output_placeholder": "The generated optimized prompt will be displayed here, you can edit it further...", "waiting_text": "Waiting for generation result...", "generating_text": "AI is generating optimized prompt...", "error_no_input": "Please enter a prompt", "success_generated": "Generation completed!", "error_generate_failed": "Generation failed, please try again", "error_no_content": "No content to copy", "success_copied": "Copied to clipboard", "error_copy_failed": "Co<PERSON> failed", "success_cleared": "Output content cleared", "favorite_button": "Favorite", "favorite_modal_title": "Save to Favorites", "favorite_modal_edit_title": "Edit Favorite", "favorite_title_label": "Title", "favorite_title_required": "Title *", "favorite_title_placeholder": "Enter favorite title", "favorite_category_label": "Category", "favorite_category_placeholder": "Select category (optional)", "favorite_category_new": "+ New Category", "favorite_category_new_placeholder": "Enter new category name", "favorite_content_label": "Prompt Content", "favorite_content_placeholder": "Enter prompt content", "favorite_notes_label": "Notes", "favorite_notes_placeholder": "Add notes (optional)", "favorite_cancel": "Cancel", "favorite_saving": "Saving...", "favorite_save": "Save Favorite", "error_title_required": "Please enter favorite title", "error_category_name_required": "Please enter new category name", "success_favorite_saved": "Favorite saved successfully", "error_favorite_failed": "Failed to save favorite", "error_get_categories_failed": "Failed to get categories", "error_get_categories_failed_with_msg": "Failed to get categories: {message}", "error_content_required": "Please enter prompt content", "error_category_exists": "Category name already exists, please use a different name", "error_category_name_too_long": "Category name must not exceed 50 characters", "error_category_name_empty": "Category name cannot be empty", "category_already_exists": "Category already exists, please use another name"}, "enhanced_prompt_generator": {"title": "Veo 3 Prompt Generator", "subtitle": "Transform your simple ideas into professional Veo 3 video generation prompts", "inputLabel": "Your Creative Idea", "inputPlaceholder": "Enter a description of the video content you want to create, e.g., a cute cat playing in the garden...", "outputLabel": "Optimized Veo 3 Prompt", "outputPlaceholder": "The optimized professional prompt will appear here...", "generateButton": "Generate", "generating": "Optimizing...", "generatingText": "AI is optimizing your prompt, please wait...", "waitingText": "Enter your creative idea and AI will generate a professional VEO-3 prompt for you", "copyButton": "Copy Prompt", "favoriteButton": "Favorite Prompt", "modeBasic": "Basic Mode(Free)", "modeAdvanced": "Advanced Mode(1 Credit)", "errorNoInput": "Please enter your creative idea", "errorGenerateFailed": "Prompt generation failed, please try again", "errorNoContent": "No content to copy", "errorCopyFailed": "Co<PERSON> failed", "errorGetCategoriesFailed": "Failed to get categories", "errorGetCategoriesFailedWithMsg": "Failed to get categories: {message}", "errorFavoriteFailed": "Failed to favorite", "successGenerated": "Prompt generated successfully!", "successCopied": "Prompt copied to clipboard", "successFavoriteSaved": "Prompt favorited successfully!"}, "chatgpt_prompt_generator": {"title": "ChatGPT Prompt Generator", "subtitle": "Transform your simple ideas into professional ChatGPT prompts", "inputLabel": "Your Task Description", "inputPlaceholder": "Enter what you want ChatGPT to help you with, e.g., write a marketing email, analyze data, create content...", "outputLabel": "Optimized ChatGPT Prompt", "outputPlaceholder": "The optimized professional ChatGPT prompt will appear here...", "generateButton": "Generate ChatGPT Prompt", "generating": "Optimizing...", "generatingText": "AI is optimizing your ChatGPT prompt, please wait...", "waitingText": "Enter your task description and AI will generate a professional ChatGPT prompt for you", "copyButton": "Copy", "favoriteButton": "Favorite", "modeBasic": "Basic Mode(Free)", "modeAdvanced": "Advanced Mode(1 Credit)", "errorNoInput": "Please enter your task description", "errorGenerateFailed": "ChatGPT prompt generation failed, please try again", "errorNoContent": "No content to copy", "errorCopyFailed": "Co<PERSON> failed", "errorGetCategoriesFailed": "Failed to get categories", "errorGetCategoriesFailedWithMsg": "Failed to get categories: {message}", "errorFavoriteFailed": "Failed to favorite", "successGenerated": "ChatGPT prompt generated successfully!", "successCopied": "ChatGPT prompt copied to clipboard", "successFavoriteSaved": "ChatGPT prompt favorited successfully!"}, "lyra_prompt": {"title": "Lyra Prompt Generator", "subtitle": "Transform vague ideas into precision-crafted AI prompts using the viral Reddit technique", "inputLabel": "Your Vague Request", "inputPlaceholder": "Enter any rough idea like 'help with meal prep', 'write marketing copy', or 'debug my code'...", "outputLabel": "Optimized Lyra Prompt", "outputPlaceholder": "Your precision-crafted Lyra prompt will appear here - ready to use with any AI platform...", "generateButton": "Generate Lyra Prompt", "generating": "Lyra is optimizing...", "generatingText": "L<PERSON> is applying the 4-D methodology to optimize your prompt...", "waitingText": "Enter your vague request and <PERSON><PERSON> will interview you to create the perfect AI prompt", "copyButton": "Copy Prompt", "favoriteButton": "Save to Favorites", "modeBasic": "Basic Mode (Quick Fix)", "modeAdvanced": "Detail Mode (Full Optimization)", "errorNoInput": "Please enter your request or idea", "errorGenerateFailed": "Lyra optimization failed, please try again", "errorNoContent": "No optimized prompt to copy", "errorCopyFailed": "Failed to copy prompt", "errorGetCategoriesFailed": "Failed to load categories", "errorGetCategoriesFailedWithMsg": "Failed to load categories: {message}", "errorFavoriteFailed": "Failed to save to favorites", "successGenerated": "Lyra prompt optimized successfully!", "successCopied": "Optimized prompt copied to clipboard", "successFavoriteSaved": "<PERSON><PERSON> prompt saved to favorites!"}, "chat_interface": {"thinking": "Thinking...", "start_conversation": "Start a conversation...", "model": "Model:", "clear": "Clear", "type_message": "Type your message...", "send": "Send"}, "veo3_json_generator": {"title": "Veo 3 JSON Prompt Generator - Create JSON prompts with AI", "description": "Generate structured JSON prompts for Google's Veo-3 video generation model. Choose from templates or use AI to convert your ideas into optimized JSON format.", "subtitle": "Transform your video ideas into structured JSON prompts for Veo-3", "templateMode": "Template Mode", "aiMode": "AI Generation Mode", "selectTemplate": "Select Template", "generateJson": "Generate JSON", "copyJson": "Copy", "copyButton": "Copy", "inputPrompt": "Input Original Prompt", "generateButton": "Generate", "generating": "Generating...", "jsonPreview": "JSON Prompt Preview", "sampleVideo": "JSON Prompt Sample Video", "successCopied": "JSON Prompt copied to clipboard successfully!", "errorCopyFailed": "Failed to copy JSON", "errorNoInput": "Please enter a prompt first", "errorGenerateFailed": "Failed to generate JSON. Please try again.", "successGenerated": "JSON generated successfully!"}, "veo3_json_prompt_ai_generator": {"inputPrompt": "Input Prompt", "generateButton": "Generate", "generating": "Generating...", "jsonPreview": "Result", "copyJson": "Copy", "successGenerated": "JSON generated successfully!", "successCopied": "JSON copied to clipboard!", "errorNoInput": "Please enter a prompt", "errorGenerateFailed": "Failed to generate JSON", "errorCopyFailed": "Failed to copy to clipboard", "advancedGenerating": "Generating...", "advancedGenerateButton": "Generate", "basicModeLabel": "Basic Mode (Free)", "advancedModeLabel": "Advanced Mode (1 Credit)", "basicModeHint": "💡 Switch to advanced mode for faster and better generation", "advancedModeHint": "⚡ Using advanced mode, consumes 1 credit for faster and more accurate results", "errorNotLoggedIn": "Please login to use advanced mode", "errorInsufficientCredits": "Insufficient credits, please purchase more credits", "creditsRemaining": "Credits remaining", "creditsInsufficient": "Insufficient credits, ", "buyCredits": "Buy Credits", "inputPlaceholder": "Enter your original video prompt here...", "emptyStateText": "Generated JSON Prompt will appear here"}}